'use client'

import { FormWrapper } from '@/components/core/FormWrapper'
import { Button } from '@/components/ui/button'
import { FormInput, FormPhoneInput } from '@/components/form'
import { profileSchema } from '@/modules/profile/schema'
import useProfileForm from '@/modules/profile/hooks/useProfileForm'
import { TProfile } from '@/modules/profile/types'
import FormImageUpload from '@/components/form/FileUpload/FormImageUpload'
import Image from 'next/image'

import ProfilePlaceholder from '/public/images/profile_placeholder.png'
import Link from 'next/link'
import { Routes } from '@/routes/routes'
import { SquarePen } from 'lucide-react'

type ProfileFormProps = {
  data: TProfile
  isEdit?: boolean
}

const ProfileForm = ({ data, isEdit = false }: ProfileFormProps) => {
  const { t, isPending, handleSubmit, defaultValues } = useProfileForm({ data })
  const schema = profileSchema()
  return (
    <FormWrapper
      schema={schema}
      defaultValues={defaultValues}
      onSubmit={handleSubmit}
      className="lg:py-12 md:py-10 sm:py-7 py-4 flex gap-5 flex-wrap"
    >
      <div className="w-full max-w-[250px]">
        {isEdit ? (
          <FormImageUpload name="image" maxSize={5} />
        ) : (
          <figure className="p-3 border border-input-border-light dark:border-input-border-dark rounded-[10px] relative max-w-[250px] max-h-[250px] flex justify-center items-center size-[250px]">
            <Image
              alt={'profile image'}
              src={data?.image ?? ProfilePlaceholder.src}
              className="object-cover object-center rounded-[10px] max-h-[200px] max-w-[200px]"
              width={200}
              height={200}
            />
          </figure>
        )}
      </div>

      <article className="flex-1">
        <div className="flex justify-between items-center flex-wrap pb-5 gap-4">
          <h2 className="text-2xl font-bold capitalize">{t('profile.profile_information')}</h2>
          {isEdit ? (
            <Button className="max-w-[160px]" type="submit" isLoading={isPending}>
              {t('button.save_changes')}
            </Button>
          ) : (
            <Link
              href={Routes.PROFILE_EDIT}
              className="bg-bg-action-light dark:bg-bg-action-dark text-primary dark:text-white max-w-[250px] flex justify-center items-center rounded-[10px] py-4 max-h-[50px] border border-input-border-light dark:border-input-border-dark min-w-[160px] font-semibold gap-2"
            >
              <SquarePen size={16} className="text-secondary dark:text-white" />
              {t('button.edit')}
            </Link>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-x-28 md:gap-x-10 border-t-[2px] border-bg-input-light dark:border-bg-input-dark py-3">
          <FormInput
            name="name"
            className=" sm:min-w-[380px]"
            label={t('label.full_name')}
            placeholder={t('label.full_name')}
            disabled={!isEdit}
          />

          <FormPhoneInput
            phoneName="phone"
            placeholder={t('label.phone')}
            label={t('label.phone')}
            disabledCountry
            disabled={!isEdit}
          />

          <FormInput
            name="email"
            className=" sm:min-w-[380px]"
            type="email"
            label={t('label.email')}
            placeholder={t('label.email')}
            disabled={!isEdit}
          />
        </div>
      </article>
    </FormWrapper>
  )
}

export default ProfileForm
