import PrivateLayoutComponent from '@/components/layouts/PrivateLayout'
import { getServerAuthSession } from '@/config/auth'
import { redirect } from 'next/navigation'

export default async function PrivateLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
    // Check authentication on server side
  const session = await getServerAuthSession()

  if (!session) {
    redirect('/auth/login')
  }
  
  return <PrivateLayoutComponent>{children}</PrivateLayoutComponent>
}
