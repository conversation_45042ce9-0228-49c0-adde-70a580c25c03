import { Routes } from '@/routes/routes'
import RegisterCard from './registerCard'
import { useTranslations } from 'next-intl'
import BreadcrumbComponent from '@/components/shared/BreadcrumbComponent'
import PageHeading from '@/components/shared/PageHeading'
import { TProfile } from '../types'

const RegisterProviderPage = ({ userData }: { userData: TProfile }) => {
  const t = useTranslations()
  const registerProviderTypes = [
    {
      image: require('/public/images/tech.png'),
      url: Routes.REGISTER_PROVIDER.TECHNICIAN,
      title: t('register_provider.register_as_technician'),
      isPending: userData?.has_pending_technician_request,
    },
    {
      image: require('/public/images/supplier.png'),
      url: Routes.REGISTER_PROVIDER.SUPPLIER,
      title: t('register_provider.register_as_supplier'),
      isPending: userData?.has_pending_supplier_request,
    },
    {
      image: require('/public/images/eng.png'),
      url: Routes.REGISTER_PROVIDER.SERVICE_PROVIDER,
      title: t('register_provider.register_as_an_equipment_rental_provider'),
      isPending: userData?.has_pending_rental_outlet_request,
    },
  ]
  return (
    <section className="container">
      <BreadcrumbComponent />
      <PageHeading text={t('register_provider.registration_type_note')} />
      <div className="flex justify-center flex-wrap lg:flex-nowrap gap-4 mb-[50px] md:mb-[100px]">
        {registerProviderTypes.map((item) => !item.isPending && <RegisterCard key={item.url} {...item} />)}
      </div>
    </section>
  )
}

export default RegisterProviderPage
