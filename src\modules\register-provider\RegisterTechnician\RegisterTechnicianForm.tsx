'use client'

import { <PERSON><PERSON>rapper } from '@/components/core/FormWrapper'
import { FormCombobox, FormInput } from '@/components/form'
import { Button } from '@/components/ui/button'
import { schema } from './form.validation'

import useRegisterTechnicianForm from './useRegisterTechnicianForm'
import FormFileUpload from '@/components/form/FileUpload/FormFileUpload'
import useApi from '@/hooks/useApi'
import FormHeader from '../components/FormHeader'
import { IRegisterTechnicianForm } from '../types'
import LocationInput from '../components/LocationInput'
// import SuccessDialog from '@/components/shared/successDialog'
import { FormMultiSelect } from '@/components/form/FormMultiSelect'

const defaultValues: IRegisterTechnicianForm = {
  phone: '',
  email: '',
  name: '',
  country_code: '',
  location: null as unknown as ILocation,
  bank_account_number: 0,
  bank_name: '',
  iban: '',
  id_number: 0,
  nationality_id: 0,
  specializations: [],
  id: 0,
  image: '',
  biometric_token: '',
  biometric_enabled: false,
  notification_enabled: false,
  is_technician: false,
  is_supplier: false,
  is_rental_support: false,
  username: '',
  has_pending_rental_outlet_request: false,
  has_pending_technician_request: false,
  has_pending_supplier_request: false,
}

const RegisterTechnicianForm = ({ userData }: { userData: IRegisterTechnicianForm }) => {
  const { state: specializations } = useApi<{ data: IDDl[] }>({
    path: '/ddl/specializations',
    method: 'GET',
    intermediate: true,
  })
  const { state: countries } = useApi<{ data: IDDl[] }>({
    path: '/ddl/countries',
    method: 'GET',
    intermediate: true,
  })

  const { t, isPending, handleSubmit, formRef, isOpenSuccessDialog, handleCloseSuccessDialog } =
    useRegisterTechnicianForm({ userData: userData })
  const technicianFormSchema = schema(t)
  return (
    <div className="bg-bg-form-light dark:bg-bg-form-dark p-6 rounded-[10px] w-full max-w-[750px] mx-auto">
      <FormHeader />
      <FormWrapper ref={formRef} schema={technicianFormSchema} defaultValues={defaultValues} onSubmit={handleSubmit}>
        <div className="space-y-2">
          <FormInput
            containerClassName="flex"
            name="name"
            label={t('label.full_name')}
            placeholder={t('label.enter_full_name')}
            required
            disabled={!!userData?.name}
          />
          <FormInput
            containerClassName="flex"
            name="username"
            label={t('label.username')}
            placeholder={t('label.enter_username')}
            required
            disabled={!!userData?.username}
          />
          <FormInput
            containerClassName="flex"
            name="id_number"
            label={t('label.national_id')}
            placeholder={t('label.enter_national_id')}
            type="number"
            required
            disabled={!!userData?.id_number}
          />
          <FormCombobox
            className="relative"
            name="nationality_id"
            label={t('label.nationality')}
            placeholder={t('label.choose_nationality')}
            valueKey="id"
            labelKey="name"
            data={countries.data?.data?.data || []}
            required
          />

          <LocationInput userData={userData} />

          <FormInput
            name="bank_name"
            label={t('label.bank_name')}
            placeholder={t('label.enter_bank_name')}
            required
            disabled={!!userData?.bank_name}
          />
          <FormInput
            name="bank_account_number"
            label={t('label.account_number')}
            placeholder={t('label.enter_account_number')}
            required
            disabled={!!userData?.bank_account_number}
          />
          <FormInput
            name="iban"
            label={t('label.iban_number')}
            placeholder={t('label.enter_iban')}
            required
            disabled={!!userData?.iban}
          />
          <FormInput
            name="phone"
            label={t('label.phone_number')}
            placeholder={t('label.enter_phone_number')}
            required
            disabled={!!userData?.phone}
          />
          <FormMultiSelect
            name="specializations"
            label={t('label.specialization')}
            placeholder={t('label.choose_specialization')}
            data={specializations.data?.data?.data || []}
            valueKey="id"
            labelKey="name"
            maxCount={2}
            required
          />
        </div>
        {userData && !userData.image && <FormFileUpload name="image" className="mt-4" />}

        <Button className="mt-9 mb-3" type="submit" isLoading={isPending}>
          {t('label.submit_request')}
        </Button>
      </FormWrapper>

      {/* <SuccessDialog
        openDialog={isOpenSuccessDialog}
        onClose={handleCloseSuccessDialog}
        title={t('dialog.request_submitted_successfully')}
        description={t('dialog.data_under_review')}
        renderButton={
          <Button type="button" className="w-full" onClick={handleCloseSuccessDialog}>
            {t('dialog.back')}
          </Button>
        }
      /> */}
    </div>
  )
}

export default RegisterTechnicianForm
