'use client'
import { useTranslations } from 'next-intl'

// shared components
import BreadcrumbComponent from '@/components/shared/BreadcrumbComponent'

// profile components
import ProfileForm from '@/modules/profile/components/ProfileForm'
import { useRemoveAccount } from '@/modules/profile/hooks/useRemoveAccount'
import CustomDialog from '@/components/shared/CustomDialog'
import { Button } from '@/components/ui/button'

// images
import Image from 'next/image'
import DeleteIcon from '/public/icons/delete_icon.svg'
import SuccessCheck from '/public/icons/tick_circle.svg'
import Trash from '/public/icons/trash.svg'

type ProfileProps = {
  data: IProfile
  title: string
  isEdit?: boolean
}

const Profile = ({ data, title, isEdit = false }: ProfileProps) => {
  const t = useTranslations()
  const {
    handleRemoveAccount,
    isPending,
    isOpenDialog,
    setIsOpenDialog,
    isOpenSuccessDialog,
    handleCloseSuccessDialog,
  } = useRemoveAccount()

  return (
    <>
      <section className="container lg:py-16 md:py-12 py-4">
        <BreadcrumbComponent title={title} />
        <ProfileForm data={data} isEdit={isEdit} />

        {!isEdit && (
          <>
            <div className="flex md:justify-end items-center">
              <Button
                type="button"
                className="bg-bg-error-light dark:bg-bg-error-dark text-text-error-light dark:text-text-error-dark max-w-[200px]"
                onClick={() => setIsOpenDialog(true)}
              >
                <Image src={Trash} alt="success" width={20} height={20} />
                {t('button.delete_account')}
              </Button>
            </div>
            {/*  delete acount dialog  */}
            <CustomDialog
              icon={<Image src={DeleteIcon} alt="delete account icon" width={56} height={56} />}
              openDialog={isOpenDialog}
              onClose={() => setIsOpenDialog(false)}
              title={t('profile.delete_account_title')}
              description={t('profile.delete_account_body')}
              renderButton={
                <div className="grid md:grid-cols-2 grid-cols-1 md:gap-4 gap-3">
                  <Button type="button" className="w-full " isLoading={isPending} onClick={() => handleRemoveAccount()}>
                    {t('button.delete')}
                  </Button>
                  <Button
                    type="button"
                    className="w-full bg-bg-form-light dark:bg-bg-form-dark  text-bg-layout-light dark:text-bg-layout-light"
                    onClick={() => setIsOpenDialog(false)}
                  >
                    {t('button.cancel')}
                  </Button>
                </div>
              }
            />

            {/* success delete acount dialog */}
            <CustomDialog
              icon={<Image src={SuccessCheck} alt="success" width={56} height={56} />}
              openDialog={isOpenSuccessDialog}
              onClose={handleCloseSuccessDialog}
              textContent={
                <p className="text-center font-semibold text-sm sm:text-base  text-text-desc-light max-w-[524px] dark:text-text-desc-dark">
                  {t('profile.is_deleted')}
                  <span className="font-bold"> {t('profile.account')} </span>
                  {t('profile.success')}
                </p>
              }
            />
          </>
        )}
      </section>
    </>
  )
}

export default Profile
