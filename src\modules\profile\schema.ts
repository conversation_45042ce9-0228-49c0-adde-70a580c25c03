import { phoneSuadiSchema } from '@/lib/schema'
import { object, string, mixed } from 'yup'

export const profileSchema = () => {
  return object({
    name: string().required(),
    email: string().email().optional(),
    phone: phoneSuadiSchema(false),
    image: mixed()
      .nullable()
      .test(
        'is-file-or-string',
        'Image must be a file or a valid URL string',
        (value) => {
          // If no value, it's valid (due to nullable)
          if (value === null || value === undefined) return true
          // If it's a File object, it's valid
          if (value instanceof File) return true
          // If it's a string (existing image URL), it's valid
          if (typeof value === 'string') return true
          return false
        }
      ),
  })
}
