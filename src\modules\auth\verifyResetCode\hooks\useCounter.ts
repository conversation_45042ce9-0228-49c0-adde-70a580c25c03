import { useEffect, useState, useCallback } from 'react'

import { observer } from '@/utils/observer'

export const useCounter = () => {
  const initialCounter =
    typeof window !== 'undefined' && sessionStorage.getItem('counter') ? Number(sessionStorage.getItem('counter')) : 60
  const [counter, setCounter] = useState<number>(initialCounter)

  const handelReCounter = useCallback(() => {
    setCounter(60)
    sessionStorage.setItem('counter', '60')
  }, [])

  // Subscribe to observer only once on mount
  useEffect(() => {
    observer.subscribe('handelReCounter', handelReCounter)
    
    return () => {
      observer.unsubscribe('handelReCounter')
    }
  }, [handelReCounter])

  // Handle counter countdown
  useEffect(() => {
    if (counter === 0) {
      observer.fire('changeStateCounterFinally')
      sessionStorage.removeItem('counter')
      return
    }
    
    const counterTime = setTimeout(() => {
      setCounter(prevCounter => {
        const newCounter = prevCounter - 1
        sessionStorage.setItem('counter', newCounter.toString())
        return newCounter
      })
    }, 1000)

    return () => {
      clearTimeout(counterTime)
    }
  }, [counter])

  return {
    counter,
    isCounterActive: counter > 0,
  }
}
