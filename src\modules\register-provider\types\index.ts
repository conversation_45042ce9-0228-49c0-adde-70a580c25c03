export type TProfile = {
  id: number
  name: string
  email: string
  country_code: string
  phone: string
  image: string
  biometric_token: string
  biometric_enabled: boolean
  notification_enabled: boolean
  is_technician: boolean
  is_supplier: boolean
  is_rental_support: boolean
  bank_account_number: number
  bank_name: string
  iban: string
  location: ILocation
  username: string
  has_pending_rental_outlet_request: boolean
  has_pending_technician_request: boolean
  has_pending_supplier_request: boolean
}

export interface IRegisterTechnicianForm extends TProfile {
  id_number: number
  specializations: number[]
  nationality_id: number
}

export interface IRegisterSupplierForm extends TProfile {
  supplier_type: 'wholesale' | 'retail'
  company_name: string
  company_email: string
  tax_number: string
  commercial_registration: string
  categories: number[]
}

export interface IAddAddressForm {}
