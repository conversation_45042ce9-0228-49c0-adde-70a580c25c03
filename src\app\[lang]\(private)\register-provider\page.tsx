import RegisterProviderPage from '@/modules/register-provider/registerType'
import { apiService } from '@/services'
import { Metadata } from 'next'

export const dynamic = 'force-dynamic'
export const metadata: Metadata = {
  title: 'Register Provider',
  description:
    'Register as a service provider by choosing your role: technician, service and equipment provider, or supplier, and start offering your services with ease.',
}

const Page = async () => {
  const res = await apiService({
    path: '/profile',
  })
  const userData: IProfile = res?.data?.data
  return <RegisterProviderPage userData={userData} />
}

export default Page
