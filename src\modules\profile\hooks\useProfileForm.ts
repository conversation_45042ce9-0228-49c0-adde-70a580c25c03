import useApi from '@/hooks/useApi'
import { TProfileUpdate } from '@/modules/profile/types'
import { createFormData } from '@/utils/createFormData'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'
import ProfilePlaceholder from '/public/images/profile_placeholder.png'
import { revalidateTagAction } from '@/services'
import { Routes } from '@/routes/routes'
import { useRouter } from 'next/navigation'

const useProfileForm = ({ data }: { data: IProfile }) => {
  const t = useTranslations()
  const router = useRouter()

  const defaultValues: TProfileUpdate = {
    image: data?.image ?? ProfilePlaceholder.src,
    name: data?.name ?? '',
    email: data?.email ?? '',
    phone: {
      identifier: data?.phone ?? '',
      country_code: data.country_code ?? '966',
    },
  }

  const { action, isPending } = useApi({
    path: '/user/profile?_method=PUT',
    method: 'POST',
    handleSuccess: false,
    onSuccess: () => {
      toast.success(t('profile.profile_updated_successfully'))
      revalidateTagAction('profile-data')
      router.push(Routes.PROFILE)
    },
  })

  const handleSubmit = async (payload: TProfileUpdate) => {
    const payloadData: any = {
      name: payload.name,
      phone: payload.phone.identifier,
      country_code: payload.phone.country_code,
      ...(payload.email && { email: payload.email }),
    }

    if (payload.image instanceof File) {
      payloadData.image = payload.image
    }
    const formdata = createFormData(payloadData)
    action(formdata)
  }

  return {
    t,
    isPending,
    handleSubmit,
    defaultValues,
  }
}

export default useProfileForm
