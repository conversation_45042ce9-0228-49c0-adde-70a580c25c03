import { FormInput } from '@/components/form'
import { Button } from '@/components/ui/button'
import { MapPin } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useState } from 'react'
import MapModal from './mapModal'

const LocationInput = (userData: IProfile) => {
  const [isOpenModal, setIsOpenModal] = useState(false)
  const t = useTranslations()
  return (
    <>
      <FormInput
        name="location"
        label={t('label.location')}
        placeholder={t('label.enter_location')}
        required
        disabled
        suffix={
          <Button
            disabled={!!userData?.location}
            onClick={() => setIsOpenModal(true)}
            type="button"
            variant="text"
            className="p-0 !w-fit border-0"
          >
            <MapPin className="text-black-500 dark:text-white" />
          </Button>
        }
      />
      <MapModal openDialog={isOpenModal} onClose={() => setIsOpenModal(false)} />
    </>
  )
}

export default LocationInput
