'use client'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader } from '@/components/ui/dialog'
import { useState, useEffect } from 'react'

import { useFormContext } from 'react-hook-form'
import { observer } from '@/utils/observer'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import Image from 'next/image'
import { DialogTitle } from '@radix-ui/react-dialog'
import { toast } from 'sonner'
import { getAddressFromCoordinates } from '@/utils/getAddressFormCoordinates'
import MapComponent from './MapComponent'

type MapModalProps = {
  openDialog: boolean
  onClose: () => void
}

const MapModal = ({ openDialog, onClose }: MapModalProps) => {
  const t = useTranslations()
  const [selectedLocation, setSelectedLocation] = useState<{ lat: number; lng: number } | null>(null)
  const [selectedAddress, setSelectedAddress] = useState<string>('')
  const [defaultLocation, setDefaultLocation] = useState<{ lat: number; lng: number }>({
    lat: -3.745,
    lng: -38.523,
  })
  const [isSaudi, setIsSaudi] = useState<boolean | null>(null)
  const { setValue } = useFormContext()

  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const lat = position.coords.latitude
          const lng = position.coords.longitude
          setDefaultLocation({ lat, lng })
          setSelectedLocation({ lat, lng })

          const { address, isSaudi } = await getAddressFromCoordinates(lat, lng)
          setSelectedAddress(address)
          setIsSaudi(isSaudi)
        },
        (error) => {
          observer.fire('notify', {
            type: 'error',
            message: error.message || error,
          })
        },
        { enableHighAccuracy: true }
      )
    }
  }, [])

  const handleMapClick = async (event: google.maps.MapMouseEvent) => {
    if (event.latLng) {
      const lat = event.latLng.lat()
      const lng = event.latLng.lng()
      setSelectedLocation({ lat, lng })

      const { address, isSaudi } = await getAddressFromCoordinates(lat, lng)
      setSelectedAddress(address)
      setIsSaudi(isSaudi)
    }
  }

  const sendLocationToBackend = async () => {
    if (!selectedLocation) return

    if (isSaudi === false) {
      toast.error(t('validations.location_not_saudi'))
      return
    }

    setValue(
      'location_map',
      selectedAddress || `https://www.google.com/maps?q=${selectedLocation.lat},${selectedLocation.lng}`
    )
    setValue('location[lat]', selectedLocation.lat)
    setValue('location[lng]', selectedLocation.lng)
    setValue('location', selectedAddress)
    onClose()
  }

  return (
    <Dialog open={openDialog} onOpenChange={onClose}>
      <DialogHeader className="sr-only hidden">
        <DialogTitle>select_location</DialogTitle>
      </DialogHeader>
      <DialogContent className="md:min-w-[750px]" withOutHeader={true}>
        <MapComponent
          defaultLocation={defaultLocation}
          selectedLocation={selectedLocation}
          handleMapClick={handleMapClick}
        />

        <div className="flex justify-between items-center mt-4">
          <div className="flex gap-2 items-center w-[50%]">
            <Image src={require('/public/icons/locationWithBG.svg')} alt="location" width={24} height={24} />
            {isSaudi === false ? (
              <p className="text-red-700 text-sm text-center">{t('validations.location_not_saudi')}</p>
            ) : (
              selectedAddress && (
                <p className="text-xs text-text-desc-light dark:text-text-desc-dark">{selectedAddress}</p>
              )
            )}
          </div>

          <div className="flex gap-5 w-[50%] justify-end">
            <Button
              type="button"
              size="sm"
              className="max-w-[140px]"
              onClick={sendLocationToBackend}
              disabled={isSaudi === false}
            >
              {t('button.save')}
            </Button>
            <Button
              type="button"
              variant="secondary"
              size="sm"
              className="max-w-[140px] dark:bg-secondary-100! dark:text-secondary!"
              onClick={onClose}
            >
              {t('button.back')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default MapModal
